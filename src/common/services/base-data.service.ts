import { Injectable, Type } from '@nestjs/common';
import { BaseEntity } from '../base-entity.js';
import { RedisService } from '../../redis/redis.service.js';
import { PaginationDto } from '../dto/pagination.dto.js';

@Injectable()
export abstract class BaseDataService<T extends BaseEntity> {
  protected entityName: string;

  constructor(
    private readonly entityType: Type<T>,
    protected readonly redisService: RedisService,
  ) {
    this.entityName = this.entityType.name;
  }

  // 抽象方法，子类必须实现
  protected abstract getKeyPrefix(): string;
  protected abstract generateId(entity: Partial<T>): Promise<string>;

  private getListKey(): string {
    const prefix = this.getKeyPrefix();
    return prefix ? `${prefix}:list` : `${this.entityName}:list`;
  }

  private getHashKey(id: string): string {
    const prefix = this.getKeyPrefix();
    return prefix ? `${prefix}:${id}` : `${this.entityName}:${id}`;
  }

  private getIdKey(): string {
    const prefix = this.getKeyPrefix();
    return prefix ? `${prefix}:id` : `${this.entityName}:id`;
  }

  private getPatternKey(): string {
    const prefix = this.getKeyPrefix();
    return prefix ? `${prefix}:*` : `${this.entityName}:*`;
  }

  async lists(page = 1, pageSize = 10): Promise<PaginationDto<T>> {
    const listKey = this.getListKey();
    const total = await this.redisService.llen(listKey);
    const start = (page - 1) * pageSize;
    const end = start + pageSize - 1;
    const idsAsStrings = await this.redisService.lrange(
      listKey,
      start,
      end,
    );
    if (idsAsStrings.length === 0) {
      return {
        data: [],
        total,
        page,
        pageSize,
      };
    }
    const hashKeys = idsAsStrings.map((id) => this.getHashKey(id));
    const results = await this.redisService.mget(...hashKeys);
    const data = results
      .filter((item) => item)
      .map((item) => JSON.parse(item as string));

    return {
      data,
      total,
      page,
      pageSize,
    };
  }

  async index(): Promise<void> {
    const listKey = this.getListKey();
    await this.redisService.del(listKey);

    const pattern = this.getPatternKey();
    let cursor = '0';
    let allEntities: T[] = [];

    do {
      const [nextCursor, keys] = await this.redisService.scan(
        cursor,
        pattern,
        100,
      );
      cursor = nextCursor;
      if (keys.length > 0) {
        const items = await this.redisService.mget(...keys);
        const entities = items
          .filter((item) => item)
          .map((item) => JSON.parse(item as string) as T);
        allEntities = allEntities.concat(entities);
      }
    } while (cursor !== '0');

    if (allEntities.length > 0) {
      // Sort by modifyTime to build the list index
      allEntities.sort((a, b) => b.modifyTime - a.modifyTime);
      const sortedIds = allEntities.map((e) => e.id);
      await this.redisService.rpush(listKey, sortedIds);
    }
  }

  async saveOrModify(entity: Partial<T>): Promise<T> {
    const now = Date.now();
    let id = entity.id;
    if (id) {
      const oldEntity = await this.get(id);
      if (oldEntity) {
        entity = { ...oldEntity, ...entity, modifyTime: now };
      } else {
        entity.createTime = now;
        entity.modifyTime = now;
      }
    } else {
      id = await this.generateId(entity);
      entity.id = id;
      entity.createTime = now;
      entity.modifyTime = now;
    }

    await this.redisService.set(
      this.getHashKey(id),
      JSON.stringify(entity),
    );

    const listKey = this.getListKey();
    await this.redisService.lrem(listKey, 0, id);
    await this.redisService.lpush(listKey, id);

    return entity as T;
  }

  async remove(id: string): Promise<void> {
    await this.redisService.del(this.getHashKey(id));
    await this.redisService.lrem(this.getListKey(), 0, id);
  }

  async get(id: string): Promise<T | null> {
    const result = await this.redisService.get(this.getHashKey(id));
    if (result) {
      return JSON.parse(result) as T;
    }
    return null;
  }
}