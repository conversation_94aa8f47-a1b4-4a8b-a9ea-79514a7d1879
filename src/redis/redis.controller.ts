import {
  Controller,
  Get,
  Post,
  Delete, // Added Delete
  Param,
  Body,
  NotFoundException,
  BadRequestException,
  Req,
  Res,
  HttpStatus,
  UseGuards
} from '@nestjs/common';
import { RedisService } from './redis.service.js';
import { DeleteKeysDto } from './dto/delete-keys.dto.js'; // Added DTO import
import { Request, Response } from 'express';
import { AuthPermissions } from '../common/decorators/auth.decorator.js';
import { PermissionLevel } from '../common/decorators/auth.enum.js';
import { AuthGuard } from '../common/guards/auth.guard.js';
import { RawResponse } from '../common/decorators/raw-response.decorator.js';

@Controller('redis')
export class RedisController {
  constructor(private readonly redisService: RedisService) {}

  @Get('/:key')
  @RawResponse()
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_ONLY)
  async getValue(
    @Param('key') key: string,
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
  ): Promise<string> { // Return type changed to string
    console.log(`RedisController.getValue: key='${key}'`);
    const data = await this.redisService.get(key);
    console.log(`RedisController.getValue: data from service for key='${key}':`, data);

    if (data === null) {
      throw new NotFoundException(`Value for key '${key}' not found`);
    }

    // Always return plain text
    res.type('text/plain');
    console.log(`RedisController.getValue: returning as text/plain for key='${key}':`, data);
    return data;
  }

  @Post('/:key')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async setValue(
    @Param('key') key: string,
    @Body() body: any,
    @Req() req: Request,
  ): Promise<{ success: boolean; key: string; message: string }> {
    const contentType = req.headers['content-type']?.toLowerCase();
    let valueToStore: string;

    console.log(`RedisController.setValue: Received POST for key='${key}', Content-Type='${contentType}', body type='${typeof body}'`);

    if (typeof body === 'string') {
      valueToStore = body;
      console.log(`RedisController.setValue: Body is already a string. Storing as is.`);
    } else if (typeof body === 'object' && body !== null) {
      // For objects (including JSON parsed by NestJS), stringify them.
      // This will store the raw JSON representation of the object.
      try {
        valueToStore = JSON.stringify(body);
        console.log(`RedisController.setValue: Body is an object. Storing stringified version: ${valueToStore}`);
      } catch (error) {
        console.error(`RedisController.setValue: Error stringifying object body for key='${key}':`, error);
        throw new BadRequestException('Could not stringify the provided object body.');
      }
    } else if (body === undefined || body === null) {
        // Handle cases where body might be explicitly undefined or null after parsing
        // Depending on requirements, you might want to store an empty string, a specific marker, or throw an error.
        // For now, let's store an empty string if the body is undefined/null.
        valueToStore = '';
        console.log(`RedisController.setValue: Body is undefined or null. Storing empty string for key='${key}'.`);
    }
    else {
      // For other types (e.g., number, boolean if not auto-stringified by a middleware)
      // or if a custom body parser somehow results in other types.
      try {
        valueToStore = String(body);
        console.log(`RedisController.setValue: Body is of other type. Converting to string: ${valueToStore}`);
      } catch (error) {
         console.error(`RedisController.setValue: Error converting body to string for key='${key}':`, error);
        throw new BadRequestException('Could not convert the provided body to a string.');
      }
    }

    await this.redisService.set(key, valueToStore);
    console.log(`RedisController.setValue: Successfully called redisService.set for key='${key}' with value='${valueToStore.substring(0, 100)}${valueToStore.length > 100 ? '...' : ''}'`);
    return { success: true, key: key, message: 'Value set successfully' };
  }

  @Delete()
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async deleteKeys(
    @Body() deleteKeysDto: DeleteKeysDto,
  ): Promise<{ success: boolean; count: number; message: string }> {
    if (!deleteKeysDto || !deleteKeysDto.keys || deleteKeysDto.keys.length === 0) {
      throw new BadRequestException('Keys array cannot be empty.');
    }
    console.log(`RedisController.deleteKeys: keys='${deleteKeysDto.keys.join(', ')}'`);
    const count = await this.redisService.del(deleteKeysDto.keys);
    console.log(`RedisController.deleteKeys: deleted ${count} keys`);
    return { success: true, count, message: `Successfully deleted ${count} key(s).` };
  }
}