import { Injectable, Logger } from '@nestjs/common';
import { ArticleMeta } from './dto/article-meta.dto.js';
import { GithubService } from './github.service.js'; // 假设 GithubService 提供了获取文件内容的方法
import { GithubArticleDataService } from './services/github-article-data.service.js';

@Injectable()
export class GithubArticleService {
  private readonly logger = new Logger(GithubArticleService.name);

  constructor(
    private readonly githubArticleDataService: GithubArticleDataService,
    private readonly githubService: GithubService, // 注入 GithubService
  ) {}



  // 提取标题的简单实现 (取第一行非空文本，移除 #)
  private extractTitle(content: string): string {
   const lines = content.split('\n');
  for (const line of lines) {
    let trimmedLine = line.trim();
    if (trimmedLine) {
      // 去除开头的 # 和空格
      trimmedLine = trimmedLine.replace(/^#+\s*/, '');
      // 去除所有的 **
      trimmedLine = trimmedLine.replace(/\*\*/g, '');
      // 去除所有的 ---
      trimmedLine = trimmedLine.replace(/---/g, '');
      // 截取前100个字符
      return trimmedLine.substring(0, 100);
    }
  }
  return 'Untitled';
  }

  // 提取摘要的简单实现 (取前200个非空字符)
  private extractSummary(content: string): string {
    const cleanedContent = content
      // 移除 YAML frontmatter
      .replace(/---[\s\S]*?---/, '')
      // 移除代码块
      .replace(/```[\s\S]*?```/g, '')
      // 移除所有标题行（以 # 开头的一整行）
      .replace(/^#+ .*$/gm, '')
      // 移除引用行（以 > 开头的一整行）
      .replace(/^>.*$/gm, '')
      // 移除包裹的星号，保留文本
      .replace(/\*{3}([\s\S]*?)\*{3}/g, '$1') // 将 ***text*** 替换为 text
      .replace(/\*{2}([\s\S]*?)\*{2}/g, '$1') // 将 **text** 替换为 text
      .replace(/\*([\s\S]*?)\*/g, '$1')       // 将 *text* 替换为 text
      // 替换多余的空白字符为单个空格
      .replace(/\s+/g, ' ')
      .trim();

    return cleanedContent.substring(0, 200);
  }

  async indexArticle(repo: string, path: string): Promise<ArticleMeta | null> {
    this.logger.log(`Indexing article: ${repo}/${path}`);
    try {
      let content: string | null = null;
      const fileData = await this.githubService.fetchArticle(repo, path);

      // Check if fileData is an object and not an array (which would be a directory listing)
      if (typeof fileData === 'object' && fileData !== null && !Array.isArray(fileData) && 'content' in fileData && 'encoding' in fileData) {
        if (fileData.encoding === 'base64') {
          content = Buffer.from(fileData.content, 'base64').toString('utf-8');
        } else {
          content = fileData.content; // 假设是 utf-8 或其他可直接使用的编码
        }
      } else if (Array.isArray(fileData)) {
        this.logger.warn(`Attempted to index a directory path as a single article: ${repo}/${path}. Skipping.`);
        return null;
      }

      if (!content) {
        this.logger.warn(`Content not found for ${repo}/${path}, attempting to remove from index.`);
        await this.removeArticle(repo, path);
        return null;
      }

      const title = this.extractTitle(content);
      const summary = this.extractSummary(content);
      const now = new Date();

      // 检查是否已存在（path 就是 ID）
      const existingArticle = await this.githubArticleDataService.getByRepo(repo, path);

      const articleMeta = new ArticleMeta();
      articleMeta.title = title;
      articleMeta.summary = summary;
      articleMeta.repo = repo;
      articleMeta.path = path;
      articleMeta.createdAt = now;
      articleMeta.updatedAt = now;

      if (existingArticle) {
        // 更新现有文章，保留原始创建时间和ID
        articleMeta.id = existingArticle.id;
        articleMeta.createdAt = existingArticle.createdAt;
        articleMeta.createTime = existingArticle.createTime;
      }

      const savedArticle = await this.githubArticleDataService.saveOrModifyByRepo(repo, articleMeta);
      this.logger.log(`Successfully indexed article: ${repo}/${path}`);
      return savedArticle;
    } catch (error) {
      this.logger.error(`Error indexing article ${repo}/${path}: ${error.message}`, error.stack);
      if (error.status === 404) { // Github 返回 404
        this.logger.warn(`File ${repo}/${path} not found on GitHub, removing from index.`);
        await this.removeArticle(repo, path);
      }
      return null;
    }
  }

  async removeArticle(repo: string, path: string): Promise<boolean> {
    const removed = await this.githubArticleDataService.removeByRepoAndPath(repo, path);
    if (removed) {
      this.logger.log(`Successfully removed article from index: ${repo}/${path}`);
      return true;
    }
    this.logger.log(`Article not found in index for removal or already removed: ${repo}/${path}`);
    return false;
  }

  async getArticle(repo: string, path: string): Promise<ArticleMeta | null> {
    return await this.githubArticleDataService.getByRepo(repo, path);
  }

  async listArticlesByRepo(repo: string, page = 1, pageSize = 10): Promise<{ articles: ArticleMeta[], total: number }> {
    this.logger.log(`获取文章列表，仓库: ${repo}, 页码: ${page}, 每页数量: ${pageSize}`);
    return await this.githubArticleDataService.findByRepo(repo, page, pageSize);
  }

  /**
   * 处理 GitHub push webhook 事件
   */
  async buildArticleIndex(payload: any): Promise<void> {
    let repo = payload.repository?.name;
    this.logger.log(`开始处理 push webhook 事件，仓库: ${repo}`);

    if (!repo) {
      this.logger.warn(`Push 事件缺少 repository.name 字段`);
      return;
    }

    // 然后处理文章索引
    this.logger.log(`开始文章索引处理，仓库: ${repo}`);
    const commits = payload.commits || [];
    let indexedCount = 0; // 计数器，暂时保留用于日志或快速反馈，精确计数在 allSettled 后
    let removedCount = 0; // 同上
    const indexPromises: Promise<any>[] = [];
    const removePromises: Promise<any>[] = [];


    for (const commit of commits) {
      const processFiles = (files: string[], action: 'added' | 'modified' | 'removed') => {
        for (const filePath of files) {
          if (filePath.match(/\.(md|mdx)$/i)) { // 只处理 markdown 文件
            if (action === 'removed') {
              //收集删除操作
              removePromises.push(this.removeArticle(repo, filePath));
              removedCount++; // 暂时先在这里计数，后面根据 Prmise 结果修正可能更准确
            } else { // 'added' 或 'modified'
              // 收集索引操作
              indexPromises.push(this.indexArticle(repo, filePath));
              indexedCount++; // 暂时先在这里计数
            }
          }
        }
      };

      processFiles(commit.added || [], 'added');
      processFiles(commit.modified || [], 'modified');
      processFiles(commit.removed || [], 'removed');
    }

    // 并发执行所有收集到的操作
    const indexResults = await Promise.allSettled(indexPromises);
    const removeResults = await Promise.allSettled(removePromises);

    // 可以根据 allSettled 的结果更精确地计数成功和失败的操作
    const successfulIndices = indexResults.filter(r => r.status === 'fulfilled' && r.value).length;
    const failedIndices = indexResults.length - successfulIndices;
    const successfulRemovals = removeResults.filter(r => r.status === 'fulfilled' && r.value).length;
    const failedRemovals = removeResults.length - successfulRemovals;


    this.logger.log(`文章索引处理完成，仓库: ${repo}。成功索引/更新: ${successfulIndices}, 失败: ${failedIndices}。成功删除: ${successfulRemovals}, 失败: ${failedRemovals}`);
  }

  async reindexDirectory(repo: string, directoryPath: string): Promise<{ indexed: number; errors: number }> {
    this.logger.log(`Starting reindex for directory: ${repo}/${directoryPath}`);
    let currentLevelIndexedCount = 0;
    let currentLevelErrorCount = 0;
    let totalIndexedCount = 0;
    let totalErrorCount = 0;
    const indexPromises: Promise<ArticleMeta | null>[] = [];

    try {
      const dirContents: any = await this.githubService.fetchArticle(repo, directoryPath);

      const processItem = async (item: any) => {
        if (item.type === 'file' && item.name && item.path && item.name.match(/\.(md|mdx)$/i)) {
          this.logger.debug(`Queueing index for file: ${item.path} in ${repo}`);
          // 收集索引文件的 Promise
          indexPromises.push(this.indexArticle(repo, item.path));
        } else if (item.type === 'dir' && item.path) {
          this.logger.debug(`Recursively reindexing subdirectory: ${item.path} in ${repo}`);
          // 等待递归调用完成，并累加结果
          const subDirResult = await this.reindexDirectory(repo, item.path);
          totalIndexedCount += subDirResult.indexed;
          totalErrorCount += subDirResult.errors;
        }
      };

      if (Array.isArray(dirContents)) { // Legacy support
        for (const item of dirContents) {
          await processItem(item); // 虽然 processItem 内部可能是异步的，但这里收集 Promise，递归是 await
        }
      } else if (typeof dirContents === 'object' && dirContents !== null && 'type' in dirContents) {
        if (dirContents.type === 'dir' && Array.isArray(dirContents.entries)) {
          for (const item of dirContents.entries) {
            await processItem(item); // 同上
          }
        } else if (dirContents.type === 'file') {
          const fileItem = dirContents as { type: string; name?: string; path?: string; [key: string]: any };
          if (fileItem.name && fileItem.path && fileItem.name.match(/\.(md|mdx)$/i)) {
             this.logger.debug(`Path ${directoryPath} is a file. Queueing index for file: ${fileItem.path} in ${repo}`);
             indexPromises.push(this.indexArticle(repo, fileItem.path));
          } else {
              this.logger.warn(`Path ${directoryPath} in ${repo} is a single file but not a markdown file, or type is unknown/missing name/path. Skipping.`);
          }
        }
      } else {
        this.logger.warn(`Could not retrieve directory contents for ${repo}/${directoryPath}. It might be empty, non-existent, or an unsupported type. Received: ${JSON.stringify(dirContents)}`);
      }

      // ---- 并发处理当前层级的文件 ----
      if (indexPromises.length > 0) {
        this.logger.debug(`Executing ${indexPromises.length} index operations concurrently for ${repo}/${directoryPath}`);
        const results = await Promise.allSettled(indexPromises);
        results.forEach((result, index) => {
          // 尝试从 Promise 中获取路径信息，但这可能不可靠或不存在
          // const filePathDebug = (indexPromises[index] as any)?.filePathForDebug || 'unknown path';
          const filePathDebug = `item at index ${index}`; // 更可靠的占位符

          if (result.status === 'fulfilled') {
            if (result.value) { // indexArticle 返回了 ArticleMeta 对象表示成功
              currentLevelIndexedCount++;
            } else {
              // indexArticle 返回 null 也可能是正常的（比如文件内容为空或不是文章），但也可能是错误（如404后删除）
              // 这里我们假设返回 null 算作一种处理完成，但不计入 "indexed" 计数，也不计入 "error"
              // 如果需要更精确，indexArticle 需要更好地区分成功但无内容和真正的错误
              this.logger.warn(`Indexing for a file in ${directoryPath} completed but returned null.`);
              // currentLevelErrorCount++; // 决定是否将返回 null 视为错误
            }
          } else {
            this.logger.error(`Error indexing file (${filePathDebug}) in ${directoryPath} during concurrent execution: ${result.reason?.message || result.reason}`);
            currentLevelErrorCount++;
          }
        });
      }
      // ---- 并发处理结束 ----

    } catch (error) {
      this.logger.error(`Failed to fetch or process directory ${repo}/${directoryPath}: ${error.message}`, error.stack);
      totalErrorCount++; // 将获取目录本身的错误计入总错误数
    }

    // 累加当前层级的计数
    totalIndexedCount += currentLevelIndexedCount;
    totalErrorCount += currentLevelErrorCount;

    this.logger.log(`Finished reindex for ${repo}/${directoryPath}. Total Indexed in this call (incl. subdirs): ${totalIndexedCount}, Total Errors: ${totalErrorCount}`);
    // 返回的是包括子目录在内的总计数
    return { indexed: totalIndexedCount, errors: totalErrorCount };
  }
}