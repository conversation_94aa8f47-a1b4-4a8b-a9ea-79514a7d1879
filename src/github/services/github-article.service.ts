import { Injectable, Logger } from '@nestjs/common';
import { ArticleMeta } from '../dto/article-meta.dto.js';
import { RedisService } from '../../redis/redis.service.js';
import { RepoArticleDataService } from './repo-article-data.service.js';
import { GithubService } from '../github.service.js';

@Injectable()
export class GithubArticleService {
  private readonly logger = new Logger(GithubArticleService.name);
  private repoServices: Map<string, RepoArticleDataService> = new Map();

  constructor(
    private readonly redisService: RedisService,
    private readonly githubService: GithubService,
  ) {}

  // 获取或创建特定 repo 的数据服务
  private getRepoService(repo: string): RepoArticleDataService {
    if (!this.repoServices.has(repo)) {
      const service = new RepoArticleDataService(repo, this.redisService);
      this.repoServices.set(repo, service);
    }
    return this.repoServices.get(repo)!;
  }

  // 保存或修改文章
  private async saveOrModifyByRepo(repo: string, entity: Partial<ArticleMeta>): Promise<ArticleMeta> {
    const service = this.getRepoService(repo);
    return await service.saveOrModify(entity);
  }

  // 根据 repo 获取文章列表
  private async findByRepo(repo: string, page = 1, pageSize = 10): Promise<{ articles: ArticleMeta[], total: number }> {
    const service = this.getRepoService(repo);
    const result = await service.lists(page, pageSize);
    return {
      articles: result.data,
      total: result.total
    };
  }

  // 根据 repo 和 path 删除文章（path 就是 ID）
  private async removeByRepoAndPath(repo: string, path: string): Promise<boolean> {
    const service = this.getRepoService(repo);
    const article = await service.get(path);
    if (article) {
      await service.remove(path);
      return true;
    }
    return false;
  }

  // 根据 ID 获取文章
  private async getByRepo(repo: string, id: string): Promise<ArticleMeta | null> {
    const service = this.getRepoService(repo);
    return await service.get(id);
  }


  /**
   * 移除 Markdown 结构性元素
   * @param content - Markdown 内容
   * @returns 清理后的内容
   */
  private removeMarkdownStructures(content: string): string {
    let cleanContent = content;

    // 移除 YAML front matter
    cleanContent = cleanContent.replace(/^---[\s\S]*?---\n?/m, '');

    // 移除引用块
    cleanContent = cleanContent.replace(/^>\s*.*$/gm, '');

    // 移除 Markdown 标题
    cleanContent = cleanContent.replace(/^#+\s+.*$/gm, '');

    // 移除代码块
    cleanContent = cleanContent.replace(/```[\s\S]*?```/g, '');

    // 移除表格
    cleanContent = cleanContent.replace(/^\|.*\|$/gm, '');
    cleanContent = cleanContent.replace(/^\|?[-:\s|]+\|?$/gm, '');

    // 移除列表标记
    cleanContent = cleanContent.replace(/^[\s]*[-*+]\s+/gm, '');
    cleanContent = cleanContent.replace(/^[\s]*\d+\.\s+/gm, '');

    // 移除水平分割线
    cleanContent = cleanContent.replace(/^[-*_]{3,}$/gm, '');

    return cleanContent;
  }

  /**
   * 规范化文本空白字符
   * @param text - 输入文本
   * @returns 规范化后的文本
   */
  private normalizeWhitespace(text: string): string {
    return text.replace(/\n\s*\n/g, '\n').replace(/\s+/g, ' ').trim();
  }

  /**
   * 提取标题
   * @param content - Markdown 内容
   * @returns 提取的标题
   */
  private extractTitle(content: string): string {
    const lines = content.split('\n');
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('# ')) {
        let title = trimmedLine.substring(2).trim();
        // 清理标题中的 markdown 符号
        title = this.cleanMarkdownText(title);
        return title;
      }
    }
    return 'Untitled';
  }

  /**
   * 清理文本中的 Markdown 符号
   * @param text - 包含 Markdown 符号的文本
   * @returns 清理后的纯文本
   */
  private cleanMarkdownText(text: string): string {
    let cleanText = text;

    // 移除 Markdown 链接，保留链接文本
    cleanText = cleanText.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');

    // 移除图片链接
    cleanText = cleanText.replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1');

    // 移除 Markdown 粗体和斜体标记
    cleanText = cleanText.replace(/\*\*\*([^*]+)\*\*\*/g, '$1'); // 粗斜体
    cleanText = cleanText.replace(/\*\*([^*]+)\*\*/g, '$1'); // 粗体
    cleanText = cleanText.replace(/\*([^*]+)\*/g, '$1'); // 斜体
    cleanText = cleanText.replace(/___([^_]+)___/g, '$1'); // 粗斜体
    cleanText = cleanText.replace(/__([^_]+)__/g, '$1'); // 粗体
    cleanText = cleanText.replace(/_([^_]+)_/g, '$1'); // 斜体

    // 移除删除线
    cleanText = cleanText.replace(/~~([^~]+)~~/g, '$1');

    // 移除行内代码
    cleanText = cleanText.replace(/`([^`]+)`/g, '$1');

    // 移除 HTML 标签
    cleanText = cleanText.replace(/<[^>]+>/g, '');

    // 规范化空白字符
    return this.normalizeWhitespace(cleanText);
  }

  /**
   * 提取摘要
   * @param content - Markdown 内容
   * @returns 提取的摘要
   */
  private extractSummary(content: string): string {
    // 移除结构性 Markdown 元素
    let cleanContent = this.removeMarkdownStructures(content);

    // 使用通用清理方法清理剩余的 markdown 符号
    cleanContent = this.cleanMarkdownText(cleanContent);

    // 规范化空白字符
    cleanContent = this.normalizeWhitespace(cleanContent);

    // 获取前150个字符作为摘要
    const summary = cleanContent.substring(0, 150);
    return summary.length < cleanContent.length ? summary + '...' : summary;
  }

  async indexArticle(repo: string, path: string, content: string): Promise<ArticleMeta | null> {
    this.logger.log(`Indexing article: ${repo}/${path}`);
    try {
      if (!content) {
        this.logger.warn(`Content not found for ${repo}/${path}, attempting to remove from index.`);
        await this.removeArticle(repo, path);
        return null;
      }

      const title = this.extractTitle(content);
      const summary = this.extractSummary(content);
      const now = Date.now();

      // 检查是否已存在（path 就是 ID）
      const existingArticle = await this.getByRepo(repo, path);

      const articleMeta = new ArticleMeta();
      articleMeta.title = title;
      articleMeta.summary = summary;
      articleMeta.repo = repo;
      articleMeta.path = path;
      articleMeta.createTime = now;
      articleMeta.modifyTime = now;

      if (existingArticle) {
        // 更新现有文章，保留原始创建时间和ID
        articleMeta.id = existingArticle.id;
        articleMeta.createTime = existingArticle.createTime;
      }

      const savedArticle = await this.saveOrModifyByRepo(repo, articleMeta);
      this.logger.log(`Successfully indexed article: ${repo}/${path}`);
      return savedArticle;
    } catch (error) {
      this.logger.error(`Error indexing article ${repo}/${path}: ${error.message}`, error.stack);
      if (error.status === 404) { // Github 返回 404
        this.logger.warn(`File ${repo}/${path} not found on GitHub, removing from index.`);
        await this.removeArticle(repo, path);
      }
      return null;
    }
  }

  async removeArticle(repo: string, path: string): Promise<boolean> {
    const removed = await this.removeByRepoAndPath(repo, path);
    if (removed) {
      this.logger.log(`Successfully removed article from index: ${repo}/${path}`);
      return true;
    }
    this.logger.log(`Article not found in index for removal or already removed: ${repo}/${path}`);
    return false;
  }

  async getArticle(repo: string, path: string): Promise<ArticleMeta | null> {
    return await this.getByRepo(repo, path);
  }

  async getArticles(repo: string, page = 1, pageSize = 10): Promise<{ articles: ArticleMeta[], total: number, page: number, pageSize: number }> {
    const result = await this.findByRepo(repo, page, pageSize);
    return {
      ...result,
      page,
      pageSize
    };
  }

  /**
   * 递归重新索引指定目录下的所有 Markdown 文件
   * @param repo - 仓库名称
   * @param directoryPath - 目录路径
   * @returns 索引结果统计
   */
  async reindexDirectoryRecursive(repo: string, directoryPath: string): Promise<{ indexed: number; errors: number }> {
    this.logger.log(`Starting reindex for directory: ${repo}/${directoryPath}`);

    let totalIndexedCount = 0;
    let totalErrorCount = 0;
    let currentLevelIndexedCount = 0;
    let currentLevelErrorCount = 0;

    const indexPromises: Promise<any>[] = [];

    try {
      const dirContents: any = await this.githubService.fetchArticle(repo, directoryPath);

      const processItem = async (item: any) => {
        if (item.type === 'file' && item.name && item.path && item.name.match(/\.(md|mdx)$/i)) {
          this.logger.debug(`Queueing index for file: ${item.path} in ${repo}`);
          // 收集索引文件的 Promise
          indexPromises.push(this.indexSingleArticle(repo, item.path));
        } else if (item.type === 'dir' && item.path) {
          this.logger.debug(`Recursively reindexing subdirectory: ${item.path} in ${repo}`);
          // 等待递归调用完成，并累加结果
          const subDirResult = await this.reindexDirectoryRecursive(repo, item.path);
          totalIndexedCount += subDirResult.indexed;
          totalErrorCount += subDirResult.errors;
        }
      };

      if (Array.isArray(dirContents)) {
        // 处理目录内容数组
        for (const item of dirContents) {
          await processItem(item);
        }
      } else {
        // 处理单个文件的情况
        if (dirContents && typeof dirContents === 'object') {
          if (dirContents.type === 'file') {
            const fileItem = dirContents as { type: string; name?: string; path?: string; [key: string]: any };
            if (fileItem.name && fileItem.path && fileItem.name.match(/\.(md|mdx)$/i)) {
               this.logger.debug(`Path ${directoryPath} is a file. Queueing index for file: ${fileItem.path} in ${repo}`);
               indexPromises.push(this.indexSingleArticle(repo, fileItem.path));
            } else {
                this.logger.warn(`Path ${directoryPath} in ${repo} is a single file but not a markdown file, or type is unknown/missing name/path. Skipping.`);
            }
          }
        } else {
          this.logger.warn(`Could not retrieve directory contents for ${repo}/${directoryPath}. It might be empty, non-existent, or an unsupported type. Received: ${JSON.stringify(dirContents)}`);
        }
      }

      // 等待当前层级的所有索引操作完成
      if (indexPromises.length > 0) {
        this.logger.debug(`Waiting for ${indexPromises.length} index operations to complete for ${repo}/${directoryPath}`);
        const indexResults = await Promise.allSettled(indexPromises);

        for (const result of indexResults) {
          if (result.status === 'fulfilled') {
            if (result.value !== null) {
              currentLevelIndexedCount++;
              this.logger.debug(`Successfully indexed an article in ${repo}/${directoryPath}`);
            } else {
              currentLevelErrorCount++;
              this.logger.debug(`Failed to index an article in ${repo}/${directoryPath} (returned null)`);
            }
          } else {
            currentLevelErrorCount++;
            this.logger.error(`Error during indexing in ${repo}/${directoryPath}: ${result.reason}`);
          }
        }

        this.logger.debug(`Current level indexing completed for ${repo}/${directoryPath}. Indexed: ${currentLevelIndexedCount}, Errors: ${currentLevelErrorCount}`);
      }

    } catch (error) {
      this.logger.error(`Failed to fetch or process directory ${repo}/${directoryPath}: ${error.message}`, error.stack);
      totalErrorCount++; // 将获取目录本身的错误计入总错误数
    }

    // 累加当前层级的计数
    totalIndexedCount += currentLevelIndexedCount;
    totalErrorCount += currentLevelErrorCount;

    this.logger.log(`Finished reindex for ${repo}/${directoryPath}. Total Indexed in this call (incl. subdirs): ${totalIndexedCount}, Total Errors: ${totalErrorCount}`);
    // 返回的是包括子目录在内的总计数
    return { indexed: totalIndexedCount, errors: totalErrorCount };
  }

  /**
   * 索引单个文章的辅助方法
   */
  private async indexSingleArticle(repo: string, path: string): Promise<any> {
    try {
      const fileData = await this.githubService.fetchArticle(repo, path);

      let content: string | null = null;

      // Check if fileData is an object and not an array (which would be a directory listing)
      if (typeof fileData === 'object' && fileData !== null && !Array.isArray(fileData) && 'content' in fileData && 'encoding' in fileData) {
        if (fileData.encoding === 'base64') {
          content = Buffer.from(fileData.content, 'base64').toString('utf-8');
        } else {
          content = fileData.content; // 假设是 utf-8 或其他可直接使用的编码
        }
      } else if (Array.isArray(fileData)) {
        this.logger.warn(`Attempted to index a directory path as a single article: ${repo}/${path}. Skipping.`);
        return null;
      }

      if (!content) {
        this.logger.warn(`Content not found for ${repo}/${path}, attempting to remove from index.`);
        await this.removeArticle(repo, path);
        return null;
      }

      return await this.indexArticle(repo, path, content);
    } catch (error) {
      this.logger.error(`Error indexing article ${repo}/${path}: ${error.message}`, error.stack);
      if (error.status === 404) { // Github 返回 404
        this.logger.warn(`File ${repo}/${path} not found on GitHub, removing from index.`);
        await this.removeArticle(repo, path);
      }
      return null;
    }
  }

  /**
   * 处理 GitHub push webhook 事件
   */
  async buildArticleIndexFromWebhook(payload: any): Promise<void> {
    let repo = payload.repository?.name;
    this.logger.log(`开始处理 push webhook 事件，仓库: ${repo}`);

    if (!repo) {
      this.logger.warn(`Push 事件缺少 repository.name 字段`);
      return;
    }

    // 然后处理文章索引
    this.logger.log(`开始文章索引处理，仓库: ${repo}`);
    const commits = payload.commits || [];
    let indexedCount = 0; // 计数器，暂时保留用于日志或快速反馈，精确计数在 allSettled 后
    let removedCount = 0; // 同上
    const indexPromises: Promise<any>[] = [];
    const removePromises: Promise<any>[] = [];

    for (const commit of commits) {
      const processFiles = (files: string[], action: 'added' | 'modified' | 'removed') => {
        for (const filePath of files) {
          if (filePath.match(/\.(md|mdx)$/i)) { // 只处理 markdown 文件
            if (action === 'removed') {
              //收集删除操作
              removePromises.push(this.removeArticle(repo, filePath));
              removedCount++; // 暂时先在这里计数，后面根据 Prmise 结果修正可能更准确
            } else { // 'added' 或 'modified'
              // 收集索引操作
              indexPromises.push(this.indexSingleArticle(repo, filePath));
              indexedCount++; // 暂时先在这里计数
            }
          }
        }
      };

      processFiles(commit.added || [], 'added');
      processFiles(commit.modified || [], 'modified');
      processFiles(commit.removed || [], 'removed');
    }

    // 并发执行所有收集到的操作
    const indexResults = await Promise.allSettled(indexPromises);
    const removeResults = await Promise.allSettled(removePromises);

    // 可以根据 allSettled 的结果更精确地计数成功和失败的操作
    const successfulIndices = indexResults.filter(r => r.status === 'fulfilled' && r.value).length;
    const failedIndices = indexResults.length - successfulIndices;
    const successfulRemovals = removeResults.filter(r => r.status === 'fulfilled' && r.value).length;
    const failedRemovals = removeResults.length - successfulRemovals;

    this.logger.log(`文章索引处理完成，仓库: ${repo}。成功索引/更新: ${successfulIndices}, 失败: ${failedIndices}。成功删除: ${successfulRemovals}, 失败: ${failedRemovals}`);
  }
}
