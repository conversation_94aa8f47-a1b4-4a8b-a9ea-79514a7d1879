import { Injectable } from '@nestjs/common';
import { ArticleMeta } from '../dto/article-meta.dto.js';
import { RedisService } from '../../redis/redis.service.js';
import { RepoArticleDataService } from './repo-article-data.service.js';

@Injectable()
export class GithubArticleDataService {
  private repoServices: Map<string, RepoArticleDataService> = new Map();

  constructor(private readonly redisService: RedisService) {}

  // 获取或创建特定 repo 的数据服务
  private getRepoService(repo: string): RepoArticleDataService {
    if (!this.repoServices.has(repo)) {
      const service = new RepoArticleDataService(repo, this.redisService);
      this.repoServices.set(repo, service);
    }
    return this.repoServices.get(repo)!;
  }

  // 保存或修改文章
  async saveOrModifyByRepo(repo: string, entity: Partial<ArticleMeta>): Promise<ArticleMeta> {
    const service = this.getRepoService(repo);
    return await service.saveOrModify(entity);
  }



  // 根据 repo 获取文章列表
  async findByRepo(repo: string, page = 1, pageSize = 10): Promise<{ articles: ArticleMeta[], total: number }> {
    const service = this.getRepoService(repo);
    const result = await service.lists(page, pageSize);
    return {
      articles: result.data,
      total: result.total
    };
  }

  // 根据 repo 和 path 删除文章（path 就是 ID）
  async removeByRepoAndPath(repo: string, path: string): Promise<boolean> {
    const service = this.getRepoService(repo);
    const article = await service.get(path);
    if (article) {
      await service.remove(path);
      return true;
    }
    return false;
  }

  // 根据 ID 获取文章
  async getByRepo(repo: string, id: string): Promise<ArticleMeta | null> {
    const service = this.getRepoService(repo);
    return await service.get(id);
  }

  // 根据 ID 删除文章
  async removeByRepo(repo: string, id: string): Promise<void> {
    const service = this.getRepoService(repo);
    await service.remove(id);
  }
}
