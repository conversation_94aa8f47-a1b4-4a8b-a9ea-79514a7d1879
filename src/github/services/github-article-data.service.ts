import { Injectable } from '@nestjs/common';
import { BaseDataService } from '../../common/services/base-data.service.js';
import { ArticleMeta } from '../dto/article-meta.dto.js';
import { RedisService } from '../../redis/redis.service.js';

@Injectable()
export class GithubArticleDataService extends BaseDataService<ArticleMeta> {
  constructor(redisService: RedisService) {
    super(ArticleMeta, redisService);
  }

  // 根据 repo 和 path 查找文章
  async findByRepoAndPath(repo: string, path: string): Promise<ArticleMeta | null> {
    const pattern = `${this.entityName}:*`;
    let cursor = '0';
    
    do {
      const [nextCursor, keys] = await this.redisService.scan(cursor, pattern, 100);
      cursor = nextCursor;
      
      if (keys.length > 0) {
        const items = await this.redisService.mget(...keys);
        for (const item of items) {
          if (item) {
            const article = JSON.parse(item) as ArticleMeta;
            if (article.repo === repo && article.path === path) {
              return article;
            }
          }
        }
      }
    } while (cursor !== '0');
    
    return null;
  }

  // 根据 repo 获取文章列表
  async findByRepo(repo: string, page = 1, pageSize = 10): Promise<{ articles: ArticleMeta[], total: number }> {
    const pattern = `${this.entityName}:*`;
    let cursor = '0';
    let allArticles: ArticleMeta[] = [];
    
    do {
      const [nextCursor, keys] = await this.redisService.scan(cursor, pattern, 100);
      cursor = nextCursor;
      
      if (keys.length > 0) {
        const items = await this.redisService.mget(...keys);
        for (const item of items) {
          if (item) {
            const article = JSON.parse(item) as ArticleMeta;
            if (article.repo === repo) {
              allArticles.push(article);
            }
          }
        }
      }
    } while (cursor !== '0');
    
    // 按修改时间排序（最新的在前）
    allArticles.sort((a, b) => b.modifyTime - a.modifyTime);
    
    const total = allArticles.length;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const articles = allArticles.slice(start, end);
    
    return { articles, total };
  }

  // 根据 repo 和 path 删除文章
  async removeByRepoAndPath(repo: string, path: string): Promise<boolean> {
    const article = await this.findByRepoAndPath(repo, path);
    if (article) {
      await this.remove(article.id);
      return true;
    }
    return false;
  }
}
