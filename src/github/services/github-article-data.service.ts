import { Injectable } from '@nestjs/common';
import { BaseDataService } from '../../common/services/base-data.service.js';
import { ArticleMeta } from '../dto/article-meta.dto.js';
import { RedisService } from '../../redis/redis.service.js';

// 为特定 repo 的数据服务类
class RepoArticleDataService extends BaseDataService<ArticleMeta> {
  constructor(
    private readonly repo: string,
    redisService: RedisService
  ) {
    super(ArticleMeta, redisService);
  }

  protected getKeyPrefix(): string {
    return `${this.entityName}:${this.repo}`;
  }
}

@Injectable()
export class GithubArticleDataService {
  private repoServices: Map<string, RepoArticleDataService> = new Map();

  constructor(private readonly redisService: RedisService) {}

  // 获取或创建特定 repo 的数据服务
  private getRepoService(repo: string): RepoArticleDataService {
    if (!this.repoServices.has(repo)) {
      const service = new RepoArticleDataService(repo, this.redisService);
      this.repoServices.set(repo, service);
    }
    return this.repoServices.get(repo)!;
  }

  // 保存或修改文章
  async saveOrModifyByRepo(repo: string, entity: Partial<ArticleMeta>): Promise<ArticleMeta> {
    const service = this.getRepoService(repo);
    return await service.saveOrModify(entity);
  }

  // 根据 repo 和 path 查找文章
  async findByRepoAndPath(repo: string, path: string): Promise<ArticleMeta | null> {
    const pattern = `ArticleMeta:${repo}:*`;
    let cursor = '0';

    do {
      const [nextCursor, keys] = await this.redisService.scan(cursor, pattern, 100);
      cursor = nextCursor;

      if (keys.length > 0) {
        const items = await this.redisService.mget(...keys);
        for (const item of items) {
          if (item) {
            const article = JSON.parse(item) as ArticleMeta;
            if (article.path === path) {
              return article;
            }
          }
        }
      }
    } while (cursor !== '0');

    return null;
  }

  // 根据 repo 获取文章列表
  async findByRepo(repo: string, page = 1, pageSize = 10): Promise<{ articles: ArticleMeta[], total: number }> {
    const service = this.getRepoService(repo);
    const result = await service.lists(page, pageSize);
    return {
      articles: result.data,
      total: result.total
    };
  }

  // 根据 repo 和 path 删除文章
  async removeByRepoAndPath(repo: string, path: string): Promise<boolean> {
    const article = await this.findByRepoAndPath(repo, path);
    if (article) {
      const service = this.getRepoService(repo);
      await service.remove(article.id);
      return true;
    }
    return false;
  }

  // 根据 ID 获取文章
  async getByRepo(repo: string, id: number): Promise<ArticleMeta | null> {
    const service = this.getRepoService(repo);
    return await service.get(id);
  }

  // 根据 ID 删除文章
  async removeByRepo(repo: string, id: number): Promise<void> {
    const service = this.getRepoService(repo);
    await service.remove(id);
  }
}
