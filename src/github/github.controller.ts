// src/github/github.controller.ts
import { <PERSON>, <PERSON>, Lo<PERSON>, <PERSON><PERSON>, Query, <PERSON>s, Req, Post, Body, UseGuards, HttpException, HttpStatus, RawBodyRequest, Headers, NotFoundException } from '@nestjs/common'; // Added Headers, NotFoundException
import { GithubService } from './github.service.js';
import { GithubArticleService } from './github-article.service.js'; // Added
import { Response, Request } from 'express';
import { ConfigService } from '@nestjs/config';
import { Webhooks } from '@octokit/webhooks';
import { OctokitResponse, Endpoints } from "@octokit/types";
import { AuthGuard } from '../common/guards/auth.guard.js';
import { AuthenticatedUser } from '../auth/dto/authenticated-user.dto.js';
import { CurrentUser } from '../auth/decorators/current-user.decorator.js';
import { PermissionLevel } from '../common/decorators/auth.enum.js';
import { AuthPermissions } from '../common/decorators/auth.decorator.js';
import { ReindexDirectoryDto } from './dto/reindex-directory.dto.js'; // Added

@Controller('/github')
export class GithubController {
  private readonly logger = new Logger(GithubController.name);
  private readonly webhooks: Webhooks; // Keep if still used for other events or prefer its parsing

  constructor(
    private readonly githubService: GithubService,
    private readonly githubArticleService: GithubArticleService, // Injected
    private readonly configService: ConfigService,
  ) {
    const secret = this.configService.get<string>('API_KEY_READ_WRITE'); 
    if (!secret) {
      this.logger.error('API_KEY_READ_WRITE is not configured. Webhook verification will fail.');
      this.webhooks = new Webhooks({ secret: 'dummy_secret_due_to_missing_env_var' });
    } else {
      this.webhooks = new Webhooks({ secret });
    }
  }

  @Get('/:repo/articles')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.PUBLIC)
  async getArticles(
    @Param('repo') repo: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @CurrentUser() user: AuthenticatedUser
  ): Promise<{ articles: any[], total: number, page: number, limit: number, totalPages: number }> {
    this.logger.debug(`获取文章列表，仓库: ${repo}, 页码: ${page}, 每页数量: ${limit}`);
    
    if ("gitbook" !== repo && (!user || user.provider !== "github" || user.username !== "pnparadise")) {
      throw new HttpException("资源未授权", HttpStatus.FORBIDDEN);
    }

    const pageNum = Math.max(1, parseInt(page, 10) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit, 10) || 10)); // 限制每页最多100条

    const result = await this.githubArticleService.listArticlesByRepo(repo, pageNum, limitNum);
    
    return {
      articles: result.articles,
      total: result.total,
      page: pageNum,
      limit: limitNum,
      totalPages: Math.ceil(result.total / limitNum)
    };
  }

  @Post('/:repo/reindex-directory') // New Endpoint
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE) // Assuming this requires admin privileges
  async reindexDirectory(
    @Param('repo') repo: string,
    @Body() reindexDto: ReindexDirectoryDto,
  ): Promise<{ message: string; indexed: number; errors: number; repo: string; directory: string }> {
    this.logger.log(`Reindex directory request for repo: ${repo}, path: ${reindexDto.directoryPath} `);
    // Add specific user/permission checks if necessary, e.g. only repo owner or admin

    const result = await this.githubArticleService.reindexDirectory(repo, reindexDto.directoryPath);

    return {
      message: `Directory reindex process completed for ${repo}/${reindexDto.directoryPath}.`,
      indexed: result.indexed,
      errors: result.errors,
      repo: repo,
      directory: reindexDto.directoryPath
    };
  }

  @Post('/:repo/index-article') // New Endpoint for indexing single article
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async indexArticle(
    @Param('repo') repo: string,
    @Body() body: { path: string },
  ): Promise<{ message: string; success: boolean; repo: string; path: string; article?: any }> {
    this.logger.log(`Index article request for repo: ${repo}, path: ${body.path}`);

    const result = await this.githubArticleService.indexArticle(repo, body.path);

    if (result) {
      return {
        message: `Article indexed successfully: ${repo}/${body.path}`,
        success: true,
        repo: repo,
        path: body.path,
        article: result
      };
    } else {
      return {
        message: `Failed to index article: ${repo}/${body.path}`,
        success: false,
        repo: repo,
        path: body.path
      };
    }
  }


  @Get('/:repo/*')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.PUBLIC)
  async getRepoContent(
    @Param('repo') repo: string,
    @Req() request: Request,
    @CurrentUser() user: AuthenticatedUser
  ): Promise<Endpoints['GET /repos/{owner}/{repo}/contents/{path}']['response']['data']>{
    this.logger.debug(repo, user)
    if("gitbook" !== repo && (!user || user.provider !== "github" || user.username !== "pnparadise")) {
      throw new HttpException("资源未授权", HttpStatus.FORBIDDEN);
    }
    const baseRoutePath = `/github/${repo}/`;
    let extractedPath = "";

    if (request.url.startsWith(baseRoutePath)) {
      extractedPath = request.url.substring(baseRoutePath.length);
    } else {
      // This case should ideally not happen if route matching is correct
      // but as a fallback, consider the part of URL after /github/
      const githubPrefix = "/github/";
      if(request.url.startsWith(githubPrefix)) {
        const pathAfterGithub = request.url.substring(githubPrefix.length);
        const firstSlashIndex = pathAfterGithub.indexOf('/');
        if (firstSlashIndex !== -1 && pathAfterGithub.substring(0, firstSlashIndex) === repo) {
           extractedPath = pathAfterGithub.substring(firstSlashIndex + 1);
        }
      }
    }
    
    // Remove query parameters from extractedPath
    const queryIndex = extractedPath.indexOf('?');
    if (queryIndex !== -1) {
      extractedPath = extractedPath.substring(0, queryIndex);
    }

    const actualPath = decodeURIComponent(extractedPath);

    this.logger.log(`GithubController: getRepoContent called. Repo: '${repo}', Manually Extracted Path: '${actualPath}', Request URL: '${request.url}'`);
    
    return await this.githubService.fetchArticle(repo, actualPath);
  }

 
  @Post('/:repo/webhook')
  async handleWebhook(
    @Param('repo') repo: string,
    @Body() payload: any, // NestJS parsed body, for the service layer
    @Req() request: RawBodyRequest<Request>, // To access rawBody for signature verification
  ): Promise<void> {
    const githubEvent = request.headers['x-github-event'] as string;
    const signature = request.headers['x-hub-signature-256'] as string;

    this.logger.log(`Received webhook event: ${githubEvent} for repo: ${repo}, signature: ${signature}`);

    if (!signature) {
      this.logger.warn(`Request for repo ${repo} is missing X-Hub-Signature-256 header.`);
      throw new HttpException('Missing X-Hub-Signature-256 header', HttpStatus.UNAUTHORIZED);
    }
    
    if (!request.rawBody) {
      this.logger.error('Raw body is not available for signature verification. Ensure NestJS is configured with rawBody: true in main.ts.');
      throw new HttpException('Raw body is required for signature verification', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    const bodyString = request.rawBody.toString('utf-8');

    try {
      const isValid = await this.webhooks.verify(bodyString, signature);
      if (!isValid) {
        this.logger.warn(`Invalid signature for webhook event for repo ${repo}.`);
        throw new HttpException('Invalid signature', HttpStatus.FORBIDDEN);
      }
    } catch (error) {
        this.logger.error(`Error during webhook signature verification for repo ${repo}: ${error.message}`, error.stack);
        // @octokit/webhooks might throw an error for various reasons (e.g. malformed signature)
        throw new HttpException(`Signature verification failed: ${error.message}`, HttpStatus.BAD_REQUEST);
    }
    
    this.logger.log(`Webhook signature verified successfully for repo: ${repo}`);

    // The 'payload' from @Body() is already parsed JSON.
    // No need to JSON.parse(bodyString) again if @Body() is used for the payload.
    
    if (githubEvent === 'push') {
      // 处理 push 事件 - 委托给 article service，它会处理所有逻辑

      // 首先调用原有的 githubService 处理逻辑（如缓存清理等）
      await this.githubService.cleanDataCache(payload);

      await this.githubArticleService.buildArticleIndex(payload);

    } else if (githubEvent === 'ping') {
        this.logger.log('Received ping event. Replying successfully.');
    } else {
      this.logger.log(`Ignoring webhook event: ${githubEvent} for article indexing as it is not a 'push' or 'ping' event.`);
    }
    // GitHub expects a 2xx response to acknowledge receipt of the webhook.
    // Implicitly returns 200 OK if no error is thrown.
  }
}