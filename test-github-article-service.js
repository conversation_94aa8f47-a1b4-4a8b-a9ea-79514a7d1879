// 简单测试脚本来验证 GithubArticleDataService 的新架构（每个repo独立服务）
const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module.js');

async function testGithubArticleService() {
  console.log('启动应用...');
  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    const githubArticleDataService = app.get('GithubArticleDataService');

    // 测试多个仓库的并发操作
    console.log('测试多仓库并发操作...');

    const testArticle1 = {
      title: '测试文章1',
      summary: '这是仓库1的测试文章',
      repo: 'repo1',
      path: 'docs/test1.md',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const testArticle2 = {
      title: '测试文章2',
      summary: '这是仓库2的测试文章',
      repo: 'repo2',
      path: 'docs/test2.md',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 并发保存到不同仓库
    const [savedArticle1, savedArticle2] = await Promise.all([
      githubArticleDataService.saveOrModifyByRepo('repo1', testArticle1),
      githubArticleDataService.saveOrModifyByRepo('repo2', testArticle2)
    ]);

    console.log('并发保存成功:');
    console.log('- Repo1:', savedArticle1);
    console.log('- Repo2:', savedArticle2);

    // 验证数据隔离
    console.log('验证数据隔离...');
    const repo1Articles = await githubArticleDataService.findByRepo('repo1', 1, 10);
    const repo2Articles = await githubArticleDataService.findByRepo('repo2', 1, 10);

    console.log('Repo1 文章数量:', repo1Articles.total);
    console.log('Repo2 文章数量:', repo2Articles.total);

    // 验证跨仓库查找不会混淆
    const foundInRepo1 = await githubArticleDataService.findByRepoAndPath('repo1', 'docs/test1.md');
    const notFoundInRepo2 = await githubArticleDataService.findByRepoAndPath('repo2', 'docs/test1.md');

    console.log('在 repo1 中找到文章:', !!foundInRepo1);
    console.log('在 repo2 中找到 repo1 的文章:', !!notFoundInRepo2);

    // 清理测试数据
    console.log('清理测试数据...');
    await Promise.all([
      githubArticleDataService.removeByRepoAndPath('repo1', 'docs/test1.md'),
      githubArticleDataService.removeByRepoAndPath('repo2', 'docs/test2.md')
    ]);

    console.log('所有测试完成！新架构支持多仓库并发操作且数据完全隔离。');
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await app.close();
  }
}

if (require.main === module) {
  testGithubArticleService().catch(console.error);
}
